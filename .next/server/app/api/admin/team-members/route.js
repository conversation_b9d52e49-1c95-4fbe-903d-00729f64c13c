/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/team-members/route";
exports.ids = ["app/api/admin/team-members/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fteam-members%2Froute&page=%2Fapi%2Fadmin%2Fteam-members%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fteam-members%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fteam-members%2Froute&page=%2Fapi%2Fadmin%2Fteam-members%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fteam-members%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_team_members_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/admin/team-members/route.ts */ \"(rsc)/./src/app/api/admin/team-members/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/team-members/route\",\n        pathname: \"/api/admin/team-members\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/team-members/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/app/api/admin/team-members/route.ts\",\n    nextConfigOutput,\n    userland: _Volumes_Files_Technoloway_New_Website_Technoloway_src_app_api_admin_team_members_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/admin/team-members/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fteam-members%2Froute&page=%2Fapi%2Fadmin%2Fteam-members%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fteam-members%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/team-members/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/admin/team-members/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function GET(request) {\n    try {\n        console.log('Team Members API: Starting request');\n        const { searchParams } = new URL(request.url);\n        const search = searchParams.get('search') || '';\n        const sortBy = searchParams.get('sortBy') || 'updatedat';\n        const sortOrder = searchParams.get('sortOrder') || 'desc';\n        const limit = parseInt(searchParams.get('limit') || '100');\n        const page = parseInt(searchParams.get('page') || '1');\n        const offset = (page - 1) * limit;\n        console.log('Team Members API: Parameters:', {\n            search,\n            sortBy,\n            sortOrder,\n            limit,\n            page,\n            offset\n        });\n        // Map frontend field names to database field names\n        const fieldMapping = {\n            'updatedAt': 'updatedat',\n            'createdAt': 'createdat',\n            'profileImageUrl': 'photourl',\n            'linkedin': 'linkedinurl',\n            'status': 'isactive'\n        };\n        // Convert sortBy field name if needed\n        const dbSortBy = fieldMapping[sortBy] || sortBy;\n        console.log('Team Members API: Mapped sortBy from', sortBy, 'to', dbSortBy);\n        // Build where clause for search - DISABLED for client-side search\n        // const whereClause = search ? {\n        //   OR: [\n        //     { name: { contains: search, mode: 'insensitive' as const } },\n        //     { position: { contains: search, mode: 'insensitive' as const } },\n        //     { department: { contains: search, mode: 'insensitive' as const } },\n        //     { email: { contains: search, mode: 'insensitive' as const } },\n        //     { bio: { contains: search, mode: 'insensitive' as const } }\n        //   ]\n        // } : {};\n        // Temporarily disable server-side search - use client-side filtering instead\n        const whereClause = {};\n        // Get total count\n        console.log('Team Members API: Getting total count with whereClause:', whereClause);\n        const totalCount = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.teammembers.count({\n            where: whereClause\n        });\n        console.log('Team Members API: Total count:', totalCount);\n        // Get team members with pagination\n        console.log('Team Members API: Getting team members');\n        const teamMembers = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.teammembers.findMany({\n            where: whereClause,\n            orderBy: {\n                [dbSortBy]: sortOrder\n            },\n            skip: offset,\n            take: limit\n        });\n        console.log('Team Members API: Found team members:', teamMembers.length);\n        // Transform data to match the expected format\n        const transformedMembers = teamMembers.map((member)=>({\n                id: member.id.toString(),\n                name: member.name,\n                email: member.email || '',\n                position: member.position,\n                bio: member.bio || '',\n                profileImageUrl: member.photourl || '',\n                resumeUrl: member.empresumeurl || '',\n                phone: member.phone,\n                linkedin: member.linkedinurl || '',\n                // Personal Information\n                birthdate: member.birthdate?.toISOString() || '',\n                gender: member.gender || '',\n                maritalstatus: member.maritalstatus || '',\n                socialsecurityno: member.socialsecurityno || '',\n                // Employment Information\n                hiredate: member.hiredate?.toISOString() || '',\n                salary: member.salary || 0,\n                payrollmethod: member.payrollmethod || '',\n                // Address Information\n                address: member.address || '',\n                city: member.city || '',\n                state: member.state || '',\n                zipcode: member.zipcode || '',\n                country: member.country || '',\n                // Additional URLs\n                twitterurl: member.twitterurl || '',\n                githuburl: member.githuburl || '',\n                // Other\n                notes: member.notes || '',\n                displayorder: member.displayorder || 0,\n                status: member.isactive ? 'active' : 'inactive',\n                createdAt: member.createdat.toISOString(),\n                updatedAt: member.updatedat?.toISOString() || member.createdat.toISOString()\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            posts: transformedMembers,\n            totalCount: totalCount,\n            pagination: {\n                total: totalCount,\n                page,\n                limit,\n                totalPages: Math.ceil(totalCount / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Team Members API: Error fetching team members:', error);\n        console.error('Team Members API: Error details:', {\n            name: error instanceof Error ? error.name : 'Unknown',\n            message: error instanceof Error ? error.message : 'Unknown error',\n            stack: error instanceof Error ? error.stack : 'No stack trace'\n        });\n        // Return more specific error information\n        const errorMessage = error instanceof Error ? error.message : 'Unknown database error';\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch team members',\n            details: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const teamMember = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.teammembers.create({\n            data: {\n                name: body.name,\n                email: body.email,\n                position: body.position,\n                bio: body.bio || '',\n                photourl: body.profileImageUrl || '',\n                phone: body.phone || '',\n                linkedinurl: body.linkedin || '',\n                empresumeurl: body.empresumeurl || '',\n                // Personal Information\n                birthdate: body.birthdate ? new Date(body.birthdate) : null,\n                gender: body.gender || null,\n                maritalstatus: body.maritalstatus || null,\n                socialsecurityno: body.socialsecurityno || null,\n                // Employment Information\n                hiredate: body.hiredate ? new Date(body.hiredate) : null,\n                salary: body.salary || null,\n                payrollmethod: body.payrollmethod || null,\n                // Address Information\n                address: body.address || null,\n                city: body.city || null,\n                state: body.state || null,\n                zipcode: body.zipcode || null,\n                country: body.country || null,\n                // Additional URLs\n                twitterurl: body.twitterurl || null,\n                githuburl: body.githuburl || null,\n                // Other\n                notes: body.notes || null,\n                displayorder: body.displayorder || 0,\n                isactive: body.status === 'active'\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                id: teamMember.id.toString(),\n                name: teamMember.name,\n                email: teamMember.email || '',\n                position: teamMember.position,\n                bio: teamMember.bio || '',\n                profileImageUrl: teamMember.photourl || '',\n                resumeUrl: teamMember.empresumeurl || '',\n                phone: teamMember.phone,\n                linkedin: teamMember.linkedinurl || '',\n                // Personal Information\n                birthdate: teamMember.birthdate?.toISOString() || '',\n                gender: teamMember.gender || '',\n                maritalstatus: teamMember.maritalstatus || '',\n                socialsecurityno: teamMember.socialsecurityno || '',\n                // Employment Information\n                hiredate: teamMember.hiredate?.toISOString() || '',\n                salary: teamMember.salary || 0,\n                payrollmethod: teamMember.payrollmethod || '',\n                // Address Information\n                address: teamMember.address || '',\n                city: teamMember.city || '',\n                state: teamMember.state || '',\n                zipcode: teamMember.zipcode || '',\n                country: teamMember.country || '',\n                // Additional URLs\n                twitterurl: teamMember.twitterurl || '',\n                githuburl: teamMember.githuburl || '',\n                // Other\n                notes: teamMember.notes || '',\n                displayorder: teamMember.displayorder || 0,\n                status: teamMember.isactive ? 'active' : 'inactive',\n                createdAt: teamMember.createdat.toISOString(),\n                updatedAt: teamMember.updatedat?.toISOString() || teamMember.createdat.toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error creating team member:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to create team member'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/team-members/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRTtBQUVsRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIi9Wb2x1bWVzL0ZpbGVzL1RlY2hub2xvd2F5LU5ldy1XZWJzaXRlL1RlY2hub2xvd2F5L3NyYy9saWIvcHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KClcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fteam-members%2Froute&page=%2Fapi%2Fadmin%2Fteam-members%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fteam-members%2Froute.ts&appDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2FFiles%2FTechnoloway-New-Website%2FTechnoloway&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();