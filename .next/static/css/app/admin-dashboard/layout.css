/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/admin.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   ADMIN PAGES GLOBAL STYLES
   Clean, organized CSS for admin interfaces only
   ======================================== */

/* ========================================
   ADMIN PAGE CONTAINER - RESTORED ORIGINAL
   ======================================== */

.admin-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f9fafb;
  color: #111827;
  min-height: 100vh;
  /* Override any global theme variables */
  --primary-color: #1C1B2B;
  --secondary-color: #F8F9FA;
  --text-color: #495057;
  --bg-color: #FFFFFF;
  --accent-color: #4185DD;
  --accent-secondary-color: #B42FDA;
  --divider-color: #E9ECEF;
  --error-color: rgb(230, 87, 87);
  --default-font: "Poppins", sans-serif;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --border-color: #E9ECEF;
  --card-bg: #FFFFFF;
  --hover-bg: #F8F9FA;
  /* Next.js Light Theme */
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

/* ========================================
   ADMIN PAGE LAYOUT
   ======================================== */

.admin-page * {
  box-sizing: border-box;
}

/* ========================================
   ADMIN CURSOR STYLES
   ======================================== */

.admin-page * {
  cursor: default;
}

.admin-page button,
.admin-page a,
.admin-page [role="button"],
.admin-page [onclick],
.admin-page [onClick],
.admin-page .cursor-pointer {
  cursor: pointer;
}

.admin-page button:disabled,
.admin-page a:disabled,
.admin-page [disabled] {
  cursor: not-allowed;
}

/* ========================================
   ADMIN SIDEBAR CLASSES - MOVED TO sidebar.css
   ======================================== */

/* Sidebar classes are now handled by the dedicated sidebar.css file */

/* ========================================
   ADMIN LAYOUT CONTAINERS
   ======================================== */

.admin-page .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Sidebar styles moved to sidebar.css */

.admin-page .main-content {
  flex: 1;
  padding: 2rem;
  background-color: #f9fafb;
}

/* ========================================
   ADMIN TYPOGRAPHY
   ======================================== */

.admin-page h1,
.admin-page h2,
.admin-page h3,
.admin-page h4,
.admin-page h5,
.admin-page h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.25;
  color: #111827;
}

.admin-page h1 { font-size: 1.6rem; }
.admin-page h2 { font-size: 1.6rem; }
.admin-page h3 { font-size: 1.125rem; }
.admin-page h4 { font-size: 1.25rem; }
.admin-page h5 { font-size: 1.125rem; }
.admin-page h6 { font-size: 1rem; }

.admin-page p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #374151;
}

/* ========================================
   ADMIN TABLES
   ======================================== */

/* Table styles removed to allow Tailwind classes to work properly */

/* Table header and cell styles removed to allow Tailwind classes to work properly */

/* Removed tr:hover override to allow Tailwind classes to work */

/* Selection styling overrides removed to allow Tailwind classes to work properly */

/* ========================================
   ADMIN SELECTION HIGHLIGHTING
   ======================================== */

/* Ensure blue left border selection works properly */
.admin-page tr.bg-blue-50,
.admin-page .bg-blue-50 {
  background-color: #dbeafe !important;
}

.admin-page tr.border-l-4,
.admin-page .border-l-4 {
  border-left-width: 4px !important;
}

.admin-page tr.border-l-blue-500,
.admin-page .border-l-blue-500 {
  border-left-color: #3b82f6 !important;
}

/* ========================================
   ADMIN TABLE BORDER OVERRIDES
   ======================================== */

/* Remove any extra borders that might interfere with Tailwind's divide utilities */
/* Target tables inside our wrapper to ensure clean borders */
.overflow-x-auto .rounded-lg.overflow-hidden table,
.overflow-x-auto .rounded-lg.overflow-hidden table *,
.overflow-x-auto .rounded-lg.overflow-hidden table thead,
.overflow-x-auto .rounded-lg.overflow-hidden table tbody,
.overflow-x-auto .rounded-lg.overflow-hidden table tr,
.overflow-x-auto .rounded-lg.overflow-hidden table th,
.overflow-x-auto .rounded-lg.overflow-hidden table td {
  border-top: 0 !important;
  border-bottom: 0 !important;
  border-left: 0 !important;
  border-right: 0 !important;
  border: 0 !important;
}

/* Allow Tailwind's divide-y utility to work by resetting only the borders we need */
.overflow-x-auto .rounded-lg.overflow-hidden table.divide-y > thead,
.overflow-x-auto .rounded-lg.overflow-hidden table.divide-y > tbody {
  border-top-width: 1px;
  border-color: rgb(229 231 235);
}

.overflow-x-auto .rounded-lg.overflow-hidden table tbody.divide-y > tr {
  border-top-width: 1px;
  border-color: rgb(229 231 235);
}

/* Remove the border from the first row to avoid double border with thead */
.overflow-x-auto .rounded-lg.overflow-hidden table tbody.divide-y > tr:first-child {
  border-top-width: 0 !important;
}

/* ========================================
   ADMIN NAVIGATION
   ======================================== */

.admin-page .nav {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.admin-page .nav-item {
  display: block;
  padding: 0.75rem 1rem;
  color: #374151;
  text-decoration: none;
  border-radius: 0.375rem;
  transition: all 0.15s ease;
}

.admin-page .nav-item:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.admin-page .nav-item.active {
  background-color: #dbeafe;
  color: #1e40af;
}

/* ========================================
   ADMIN BADGES AND LABELS
   ======================================== */

.admin-page .badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.25rem;
}

.admin-page .badge-primary {
  background-color: #dbeafe;
  color: #1e40af;
}

.admin-page .badge-success {
  background-color: #d1fae5;
  color: #065f46;
}

.admin-page .badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.admin-page .badge-danger {
  background-color: #fecaca;
  color: #dc2626;
}

/* ========================================
   ADMIN UTILITIES
   ======================================== */

/* Utility classes removed to prevent circular dependencies */

/* All utility classes removed to prevent circular dependencies with @apply */

/* Display utility classes removed to prevent circular dependencies */

/* ========================================
   ADMIN PAGE TITLE ICONS
   ======================================== */

/* Override default blue color for title icons to match sidebar colors */

/* Icon color overrides removed to prevent circular dependencies */

/* ========================================
   ADMIN MODAL CLASSES - RESTORED ORIGINAL
   ======================================== */

.admin-page .modal-container {
  position: fixed;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #e5e7eb;
  padding: 0;
  overflow: hidden;
  cursor: move;
  display: flex;
  flex-direction: column;
}

.admin-page .modal-container-motion {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #e5e7eb;
  padding: 0;
  overflow: hidden;
  cursor: move;
  display: flex;
  flex-direction: column;
}

.admin-page .modal-header-motion {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-bottom: 2px solid #1d4ed8;
  padding: 0.75rem 1.25rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: -0.5rem;
  cursor: move;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.admin-page .modal-header-motion.blue-gradient {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-color: #1d4ed8;
}

.admin-page .modal-header-icon {
  padding: 0.25rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
}

.admin-page .modal-header-icon-svg {
  height: 2rem;
  width: 2rem;
  color: transparent;
}

.admin-page .modal-header-content {
  flex: 1;
}

.admin-page .modal-header-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.admin-page .modal-header-subtitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.admin-page .modal-close-button {
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.15s ease;
}

.admin-page .modal-close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.admin-page .modal-close-icon {
  height: 1.5rem;
  width: 1.5rem;
  color: white;
}

.admin-page .modal-content {
  display: flex;
  flex-direction: column;
  border: 1px solid #e5e7eb;
  padding: 0;
}

.admin-page .modal-content-body {
  flex: 1;
  overflow-y: auto;
  cursor: default;
}

.admin-page .modal-form {
  padding: 1rem;
}

.admin-page .modal-form > * + * {
  margin-top: 1rem;
}

.admin-page .modal-form-section {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.admin-page .modal-form-section-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.admin-page .modal-form-section-icon {
  height: 1rem;
  width: 1rem;
  color: #2563eb;
}

.admin-page .modal-form-section-title {
  font-weight: 600;
  color: #111827;
}

.admin-page .modal-form-section-content > * + * {
  margin-top: 0.75rem;
}

/* Modal footer overrides removed - using sample-style classes instead */

/* Corner resize handle removed - using border handles only like sample modal */

/* ========================================
   ADMIN FORM CLASSES - RESTORED ORIGINAL
   ======================================== */

.admin-page .form-group {
  margin-bottom: 1rem;
}

.admin-page .form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.admin-page .form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #111827;
}

.admin-page .form-input:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6;
}

.admin-page .form-input::-moz-placeholder {
  color: #9ca3af;
}

.admin-page .form-input::placeholder {
  color: #9ca3af;
}


.admin-page .form-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #111827;
}

.admin-page .form-select:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6;
}

.admin-page .form-textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #111827;
  resize: none;
}

.admin-page .form-textarea::-moz-placeholder {
  color: #9ca3af;
}

.admin-page .form-textarea::placeholder {
  color: #9ca3af;
}

.admin-page .form-textarea:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6;
}

.admin-page .form-feedback {
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.admin-page .form-feedback.invalid {
  color: #dc2626;
}

.admin-page .form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .admin-page .form-row {
    grid-template-columns: 1fr 1fr;
  }
}

.admin-page .form-col {
  display: flex;
  flex-direction: column;
}

/* ========================================
   ADMIN BUTTON CLASSES - RESTORED ORIGINAL
   ======================================== */

/* Button overrides removed - using sample-style classes from buttons.css instead */

/* ========================================
   ADMIN RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
  .admin-page .main-content {
    padding: 1rem;
  }
  
  .admin-page .container {
    padding: 0 0.5rem;
  }
}

/* ========================================
   ADMIN DARK MODE SUPPORT
   ======================================== */

@media (prefers-color-scheme: dark) {
  .admin-page {
    background-color: #111827;
    color: #f9fafb;
  }
  
  .admin-page th {
    background-color: #374151;
   color: #f9fafb;
  }
  
  .admin-page td {
   color: #d1d5db;
  }
} 

/* ========================================
   PREVENT SIDEBAR STYLES FROM LEAKING
   ======================================== */

.admin-page main,
.admin-page .admin-content,
.admin-page .admin-table,
.admin-page .admin-form {
  /* Reset any potential font inheritance */
  font-size: inherit;
  font-weight: inherit;
}
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/buttons.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   BUTTON COMPONENT STYLES
   Reusable button styles for the application
   ======================================== */

/* ========================================
   BASE BUTTON STYLES
   ======================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.15s ease;
  font-family: var(--default-font);
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* ========================================
   SAMPLE MODAL BUTTON STYLES
   ======================================== */

.btn.sample-primary {
  padding: 8px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  margin-right: 12px;
}

.btn.sample-primary:hover {
  background-color: #2563eb;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.btn.sample-secondary {
  padding: 8px 24px;
  background-color: #6b7280;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
  transform: translateY(-2px);
}

.btn.sample-secondary:hover {
  background-color: #4b5563;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(107, 114, 128, 0.4);
}

/* ========================================
   BUTTON VARIANTS
   ======================================== */

.btn-primary {
  padding: 12px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  padding: 12px 24px;
  background-color: #6b7280;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
  transform: translateY(-2px);
}

.btn-secondary:hover:not(:disabled) {
  background-color: #4b5563;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(107, 114, 128, 0.4);
}

.btn-danger {
  padding: 12px 24px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  transform: translateY(-2px);
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
}

.btn-outline {
  padding: 12px 24px;
  background-color: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.btn-outline:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-success {
  padding: 12px 24px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
  transform: translateY(-2px);
}

.btn-success:hover:not(:disabled) {
  background-color: #059669;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.btn-warning {
  padding: 12px 24px;
  background-color: #f59e0b;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
  transform: translateY(-2px);
}

.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.4);
}

/* ========================================
   BUTTON SIZES
   ======================================== */

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn-xl {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* ========================================
   BUTTON GROUPS
   ======================================== */

.btn-group {
  display: inline-flex;
  border-radius: 0.375rem;
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  border-right-width: 1px;
}

/* ========================================
   ICON BUTTONS
   ======================================== */

.btn-icon {
  padding: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon-sm {
  padding: 0.25rem;
  width: 2rem;
  height: 2rem;
}

.btn-icon-lg {
  padding: 0.75rem;
  width: 3rem;
  height: 3rem;
}

/* ========================================
   LOADING STATES
   ======================================== */

.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 1rem;
  height: 1rem;
  top: 50%;
  left: 50%;
  margin-left: -0.5rem;
  margin-top: -0.5rem;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: btn-spin 1s linear infinite;
}

.btn-loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: btn-spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes btn-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========================================
   ADMIN/CLIENT DASHBOARD BUTTONS
   ======================================== */

/* Override any theme classes for admin/client dashboards */
.admin-page .btn-outline,
.client-page .btn-outline {
  color: #1f2937;
  border-color: #d1d5db;
}

.admin-page .btn-outline:hover:not(:disabled),
.client-page .btn-outline:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

/* ========================================
   RESPONSIVE BUTTONS
   ======================================== */

@media (max-width: 768px) {
  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  
  .btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .btn-lg {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/forms.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   FORM COMPONENT STYLES
   Reusable form element styles
   ======================================== */

/* ========================================
   BASE FORM STYLES
   ======================================== */

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: #ffffff;
  color: #111827;
  transition: border-color 0.2s ease;
  font-family: var(--default-font);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::-moz-placeholder, .form-textarea::-moz-placeholder {
  color: #9ca3af;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #9ca3af;
}

/* ========================================
   DATE INPUT STYLES (MATCHING SAMPLE MODAL)
   ======================================== */

.form-input[type="date"] {
  cursor: pointer;
  font-family: system-ui, -apple-system, sans-serif;
  line-height: 1.4;
  box-sizing: border-box;
  outline: none;
  margin: 0;
  vertical-align: baseline;
  -webkit-appearance: menulist-button;
  -moz-appearance: textfield;
  color-scheme: light;
}

.form-input[type="date"]:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.form-input[type="date"]:blur {
  border-color: #d1d5db;
  box-shadow: none;
}

/* ========================================
   FORM VALIDATION STATES
   ======================================== */

.form-input.is-valid,
.form-select.is-valid,
.form-textarea.is-valid {
  border-color: #10b981;
}

.form-input.is-valid:focus,
.form-select.is-valid:focus,
.form-textarea.is-valid:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-input.is-invalid,
.form-select.is-invalid,
.form-textarea.is-invalid {
  border-color: #dc2626;
}

.form-input.is-invalid:focus,
.form-select.is-invalid:focus,
.form-textarea.is-invalid:focus {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* ========================================
   FORM FEEDBACK
   ======================================== */

.form-feedback {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.form-feedback.valid {
  color: #10b981;
}

.form-feedback.invalid {
  color: #dc2626;
}

/* ========================================
   CHECKBOX AND RADIO STYLES
   ======================================== */

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.form-check-input {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
  accent-color: #3b82f6;
}

.form-check-label {
  font-size: 14px;
  color: #374151;
  cursor: pointer;
}

/* ========================================
   ENHANCED CHECKBOX AND RADIO STYLES (MATCHING SAMPLE MODAL)
   ======================================== */

.form-check.enhanced {
  display: flex;
  align-items: flex-start;
  gap: 0px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  line-height: 1.4;
}

.form-check.enhanced .form-check-input {
  margin: 0;
  margin-top: 2px;
}

.form-check.enhanced .form-check-label {
  margin: 0;
}

/* ========================================
   RADIO BUTTON GROUPS (MATCHING SAMPLE MODAL)
   ======================================== */

.radio-group {
  display: flex;
  gap: 16px;
}

.radio-group .form-check {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  margin-bottom: 0;
}

.radio-group .form-check-input {
  margin: 0;
}

/* ========================================
   CHECKBOX GROUPS (MATCHING SAMPLE MODAL)
   ======================================== */

.checkbox-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.checkbox-group .form-check {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-group .form-check-input {
  margin: 0;
}

/* ========================================
   FORM SIZES
   ======================================== */

.form-input-sm,
.form-select-sm,
.form-textarea-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.form-input-lg,
.form-select-lg,
.form-textarea-lg {
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

/* ========================================
   FORM LAYOUTS
   ======================================== */

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-col {
  flex: 1;
}

.form-col-auto {
  flex: 0 0 auto;
}

/* ========================================
   INPUT GROUPS
   ======================================== */

.input-group {
  display: flex;
  align-items: stretch;
}

.input-group-prepend,
.input-group-append {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #6b7280;
  font-size: 0.875rem;
}

.input-group-prepend {
  border-right: 0;
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.input-group-append {
  border-left: 0;
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.input-group .form-input {
  border-radius: 0;
  border-left: 0;
  border-right: 0;
}

.input-group .form-input:first-child {
  border-left: 1px solid #d1d5db;
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.input-group .form-input:last-child {
  border-right: 1px solid #d1d5db;
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

/* ========================================
   SEARCH INPUTS
   ======================================== */

.search-input {
  position: relative;
}

.search-input .form-input {
  padding-left: 2.5rem;
}

.search-input .search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
}

/* ========================================
   FILE INPUTS
   ======================================== */

.file-input {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.file-input input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-input-label {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #374151;
  transition: all 0.15s ease;
}

.file-input:hover .file-input-label {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

/* ========================================
   ENHANCED FILE UPLOAD STYLES (MATCHING SAMPLE MODAL)
   ======================================== */

.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  background-color: #f9fafb;
  transition: border-color 0.2s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: #9ca3af;
}

.file-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.file-upload-icon {
  width: 24px;
  height: 24px;
  color: #6b7280;
}

.file-upload-text {
  font-size: 14px;
  color: #6b7280;
}

.file-upload-subtext {
  font-size: 12px;
  color: #9ca3af;
}

/* ========================================
   ADMIN/CLIENT DASHBOARD FORMS
   ======================================== */

/* Override any theme classes for admin/client dashboards */
.admin-page .form-label,
.client-page .form-label {
  color: #374151;
}

.admin-page .form-input,
.admin-page .form-select,
.admin-page .form-textarea,
.client-page .form-input,
.client-page .form-select,
.client-page .form-textarea {
  background-color: #ffffff;
  color: #111827;
  border-color: #d1d5db;
}

.admin-page .form-input::-moz-placeholder, .admin-page .form-textarea::-moz-placeholder, .client-page .form-input::-moz-placeholder, .client-page .form-textarea::-moz-placeholder {
  color: #9ca3af;
}

.admin-page .form-input::placeholder,
.admin-page .form-textarea::placeholder,
.client-page .form-input::placeholder,
.client-page .form-textarea::placeholder {
  color: #9ca3af;
}

.admin-page .form-input:focus,
.admin-page .form-select:focus,
.admin-page .form-textarea:focus,
.client-page .form-input:focus,
.client-page .form-select:focus,
.client-page .form-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ========================================
   RESPONSIVE FORMS
   ======================================== */

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .form-col {
    margin-bottom: 1rem;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .input-group-prepend,
  .input-group-append {
    border-radius: 0;
    border: 1px solid #d1d5db;
  }
  
  .input-group-prepend {
    border-bottom: 0;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
  }
  
  .input-group-append {
    border-top: 0;
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
  }
  
  .input-group .form-input {
    border-radius: 0;
    border-left: 1px solid #d1d5db;
    border-right: 1px solid #d1d5db;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/cards.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   CARD COMPONENT STYLES
   Reusable card styles for the application
   ======================================== */

/* ========================================
   BASE CARD STYLES
   ======================================== */

.card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* ========================================
   CARD HEADER
   ======================================== */

.card-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.card-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0 0 0;
}

/* ========================================
   CARD BODY
   ======================================== */

.card-body {
  padding: 0;
}

.card-body.compact {
  padding: 1rem;
}

.card-body.spacious {
  padding: 2rem;
}

/* ========================================
   CARD FOOTER
   ======================================== */

.card-footer {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ========================================
   CARD VARIANTS
   ======================================== */

.card-primary {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.card-success {
  border-color: #10b981;
  background-color: #ecfdf5;
}

.card-warning {
  border-color: #f59e0b;
  background-color: #fffbeb;
}

.card-danger {
  border-color: #dc2626;
  background-color: #fef2f2;
}

.card-info {
  border-color: #06b6d4;
  background-color: #ecfeff;
}

/* ========================================
   CARD SIZES
   ======================================== */

.card-sm {
  padding: 1rem;
}

.card-sm .card-title {
  font-size: 1.125rem;
}

.card-lg {
  padding: 2rem;
}

.card-lg .card-title {
  font-size: 1.5rem;
}

.card-xl {
  padding: 2.5rem;
}

.card-xl .card-title {
  font-size: 1.875rem;
}

/* ========================================
   STATISTICS CARDS
   ======================================== */

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 0.25rem;
}

.stat-card .stat-change {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-card .stat-change.positive {
  color: #10b981;
}

.stat-card .stat-change.negative {
  color: #ef4444;
}

/* ========================================
   FEATURE CARDS
   ======================================== */

.feature-card {
  text-align: center;
  padding: 2rem 1.5rem;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-card .feature-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.feature-card .feature-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #111827;
}

.feature-card .feature-description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
}

/* ========================================
   PRODUCT CARDS
   ======================================== */

.product-card {
  position: relative;
  overflow: hidden;
}

.product-card .product-image {
  width: 100%;
  height: 200px;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-card .product-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #ef4444;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.product-card .product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

.product-card .product-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

/* ========================================
   TESTIMONIAL CARDS
   ======================================== */

.testimonial-card {
  background: #f8fafc;
  border: none;
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 4rem;
  color: #e5e7eb;
  font-family: serif;
}

.testimonial-card .testimonial-content {
  font-style: italic;
  margin-bottom: 1rem;
  color: #374151;
}

.testimonial-card .testimonial-author {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.testimonial-card .author-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}

.testimonial-card .author-info .author-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
}

.testimonial-card .author-info .author-title {
  font-size: 0.875rem;
  color: #6b7280;
}

/* ========================================
   ADMIN/CLIENT DASHBOARD CARDS
   ======================================== */

/* Override any theme classes for admin/client dashboards */
.admin-page .card,
.client-page .card {
  background-color: #ffffff;
  border-color: #e5e7eb;
  color: #111827;
}

/* Ensure selection highlighting works properly */
.admin-page .bg-blue-50 {
  background-color: #dbeafe !important;
}

.admin-page .border-l-4 {
  border-left-width: 4px !important;
}

.admin-page .border-l-blue-500 {
  border-left-color: #3b82f6 !important;
}

.admin-page .card-title,
.client-page .card-title {
  color: #111827;
}

.admin-page .card-subtitle,
.client-page .card-subtitle {
  color: #6b7280;
}

.admin-page .card-header,
.admin-page .card-footer,
.client-page .card-header,
.client-page .card-footer {
  border-color: #e5e7eb;
}

/* ========================================
   NAVIGATION CARDS (Clients Management Style)
   ======================================== */

/* Base Navigation Card */
.nav-card {
  position: relative;
  border-radius: 0.75rem;
  border: 2px solid;
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
  min-height: 140px;
  display: flex;
  flex-direction: column;
}

/* Navigation Card States */
.nav-card.active {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.nav-card.disabled {
  border-color: #e5e7eb;
  cursor: not-allowed;
  opacity: 0.6;
  background-color: #f9fafb;
}

.nav-card.inactive {
  border-color: #e5e7eb;
  background-color: #ffffff;
}

.nav-card.inactive:hover {
  border-color: #d1d5db;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px) scale(1.02);
  background-color: #f9fafb;
}

/* Navigation Card Color Variants - Active State */
.nav-card.active.clients {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
}

.nav-card.active.projects {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
}

.nav-card.active.invoices {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
}

.nav-card.active.payments {
  background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
  border-color: #8b5cf6;
}

.nav-card.active.categories {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
}

.nav-card.active.services {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #10b981;
}

.nav-card.active.options {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
}

.nav-card.active.features {
  background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
  border-color: #8b5cf6;
}

/* Navigation Card Content */
.nav-card-content {
  position: relative;
  padding: 0.75rem;
  border-radius: 0.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Navigation Card Header */
.nav-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.nav-card-header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Navigation Card Icon */
.nav-card-icon {
  padding: 0.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.nav-card.active .nav-card-icon.clients {
  background-color: #dbeafe;
  color: #2563eb;
}

.nav-card.active .nav-card-icon.projects {
  background-color: #d1fae5;
  color: #059669;
}

.nav-card.active .nav-card-icon.invoices {
  background-color: #fef3c7;
  color: #d97706;
}

.nav-card.active .nav-card-icon.payments {
  background-color: #e9d5ff;
  color: #7c3aed;
}

.nav-card.active .nav-card-icon.categories {
  background-color: #dbeafe;
  color: #2563eb;
}

.nav-card.active .nav-card-icon.services {
  background-color: #d1fae5;
  color: #059669;
}

.nav-card.active .nav-card-icon.options {
  background-color: #fef3c7;
  color: #d97706;
}

.nav-card.active .nav-card-icon.features {
  background-color: #e9d5ff;
  color: #7c3aed;
}

.nav-card.disabled .nav-card-icon {
  background-color: #f3f4f6;
  color: #9ca3af;
}

.nav-card.inactive .nav-card-icon {
  background-color: #f9fafb;
  color: #6b7280;
}

.nav-card.inactive:hover .nav-card-icon {
  background-color: #f3f4f6;
}

/* Navigation Card Counter */
.nav-card-counter {
  font-size: 1.125rem;
  font-weight: 700;
  transition: color 0.3s ease;
}

.nav-card.active .nav-card-counter {
  color: #111827;
}

.nav-card.disabled .nav-card-counter {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-counter {
  color: #374151;
}

.nav-card.inactive:hover .nav-card-counter {
  color: #111827;
}

/* Navigation Card Title */
.nav-card-title {
  font-size: 1.125rem;
  font-weight: 700;
  transition: color 0.3s ease;
}

.nav-card.active .nav-card-title {
  color: #111827;
}

.nav-card.disabled .nav-card-title {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-title {
  color: #374151;
}

.nav-card.inactive:hover .nav-card-title {
  color: #111827;
}

/* Navigation Card Description */
.nav-card-description {
  margin-bottom: 0.25rem;
}

.nav-card-description p {
  font-size: 0.75rem;
  line-height: 1.5;
  transition: color 0.3s ease;
}

.nav-card.active .nav-card-description p {
  color: #4b5563;
}

.nav-card.disabled .nav-card-description p {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-description p {
  color: #6b7280;
}

.nav-card.inactive:hover .nav-card-description p {
  color: #4b5563;
}

/* Navigation Card Bottom Section */
.nav-card-bottom {
  margin-top: auto;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 0;
}

/* Navigation Card Selected Name */
.nav-card-selected-name {
  flex: 1;
  min-width: 0;
}

.nav-card-selected-name-container {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  max-width: 100%;
}

.nav-card.active .nav-card-selected-name-container.clients {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e3a8a;
}

.nav-card.active .nav-card-selected-name-container.projects {
  background-color: rgba(209, 250, 229, 0.8);
  color: #064e3b;
}

.nav-card.active .nav-card-selected-name-container.invoices {
  background-color: rgba(254, 243, 199, 0.8);
  color: #92400e;
}

.nav-card.active .nav-card-selected-name-container.payments {
  background-color: rgba(233, 213, 255, 0.8);
  color: #581c87;
}

.nav-card.inactive .nav-card-selected-name-container.clients {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e40af;
}

.nav-card.inactive .nav-card-selected-name-container.projects {
  background-color: rgba(209, 250, 229, 0.8);
  color: #047857;
}

.nav-card.inactive .nav-card-selected-name-container.invoices {
  background-color: rgba(254, 243, 199, 0.8);
  color: #b45309;
}

.nav-card.inactive .nav-card-selected-name-container.payments {
  background-color: rgba(233, 213, 255, 0.8);
  color: #6b21a8;
}

.nav-card.active .nav-card-selected-name-container.categories {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e3a8a;
}

.nav-card.active .nav-card-selected-name-container.services {
  background-color: rgba(209, 250, 229, 0.8);
  color: #064e3b;
}

.nav-card.active .nav-card-selected-name-container.options {
  background-color: rgba(254, 243, 199, 0.8);
  color: #92400e;
}

.nav-card.active .nav-card-selected-name-container.features {
  background-color: rgba(233, 213, 255, 0.8);
  color: #581c87;
}

.nav-card.inactive .nav-card-selected-name-container.categories {
  background-color: rgba(219, 234, 254, 0.8);
  color: #1e40af;
}

.nav-card.inactive .nav-card-selected-name-container.services {
  background-color: rgba(209, 250, 229, 0.8);
  color: #047857;
}

.nav-card.inactive .nav-card-selected-name-container.options {
  background-color: rgba(254, 243, 199, 0.8);
  color: #b45309;
}

.nav-card.inactive .nav-card-selected-name-container.features {
  background-color: rgba(233, 213, 255, 0.8);
  color: #6b21a8;
}

.nav-card-selected-name-content {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  min-width: 0;
}

.nav-card-selected-name-dot {
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.nav-card-selected-name-dot.clients {
  background-color: #2563eb;
}

.nav-card-selected-name-dot.projects {
  background-color: #059669;
}

.nav-card-selected-name-dot.invoices {
  background-color: #d97706;
}

.nav-card-selected-name-dot.payments {
  background-color: #7c3aed;
}

.nav-card-selected-name-dot.categories {
  background-color: #2563eb;
}

.nav-card-selected-name-dot.services {
  background-color: #059669;
}

.nav-card-selected-name-dot.options {
  background-color: #d97706;
}

.nav-card-selected-name-dot.features {
  background-color: #7c3aed;
}

.nav-card-selected-name-text {
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

/* Navigation Card Row Counter (for client cards only) */
.nav-card-row-counter {
  text-align: right;
  flex-shrink: 0;
}

.nav-card-row-counter-text {
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

.nav-card.active .nav-card-row-counter-text.clients {
  color: #2563eb;
}

.nav-card.active .nav-card-row-counter-text.categories {
  color: #2563eb;
}

.nav-card.disabled .nav-card-row-counter-text {
  color: #9ca3af;
}

.nav-card.inactive .nav-card-row-counter-text {
  color: #6b7280;
}

/* Navigation Card Triangle Arrow */
.nav-card-arrow {
  position: absolute;
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 20px solid;
}

.nav-card.active .nav-card-arrow.clients {
  border-left-color: #dbeafe;
}

.nav-card.active .nav-card-arrow.projects {
  border-left-color: #d1fae5;
}

.nav-card.active .nav-card-arrow.invoices {
  border-left-color: #fef3c7;
}

.nav-card.active .nav-card-arrow.payments {
  border-left-color: #e9d5ff;
}

.nav-card.active .nav-card-arrow.categories {
  border-left-color: #dbeafe;
}

.nav-card.active .nav-card-arrow.services {
  border-left-color: #d1fae5;
}

.nav-card.active .nav-card-arrow.options {
  border-left-color: #fef3c7;
}

.nav-card.active .nav-card-arrow.features {
  border-left-color: #e9d5ff;
}

.nav-card.inactive .nav-card-arrow {
  border-left-color: #ffffff;
}

.nav-card-arrow-shadow {
  filter: drop-shadow(1px 0 0 #d1d5db);
}

/* ========================================
   RESPONSIVE CARDS
   ======================================== */

@media (max-width: 768px) {
  .card {
    padding: 1rem;
  }
  
  .card-lg {
    padding: 1.5rem;
  }
  
  .card-xl {
    padding: 2rem;
  }
  
  .feature-card {
    padding: 1.5rem 1rem;
  }
  
  .feature-card .feature-icon {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }
  
  .stat-card .stat-value {
    font-size: 1.5rem;
  }
  
  /* Navigation Cards Responsive */
  .nav-card {
    min-height: 120px;
  }
  
  .nav-card-content {
    padding: 0.5rem;
  }
  
  .nav-card-header-left {
    gap: 0.5rem;
  }
  
  .nav-card-icon {
    padding: 0.375rem;
  }
  
  .nav-card-counter {
    font-size: 1rem;
  }
  
  .nav-card-title {
    font-size: 1rem;
  }
  
  .nav-card-selected-name-container {
    padding: 0.375rem 0.5rem;
  }
  
  .nav-card-selected-name-content {
    gap: 0.25rem;
  }
  
  .nav-card-arrow {
    right: -15px;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 15px solid;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/modals.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   REUSABLE MODAL COMPONENT STYLES
   Organized by component sections for better maintainability
   
   NOTE: This file uses Tailwind CSS @apply directives and vendor-specific
   properties like text-fill-color. Linter warnings for these are expected
   and acceptable in a Tailwind CSS project.
   ======================================== */

/* ========================================
   BACKDROP STYLES
   ======================================== */

/* Mobile sidebar overlay/backdrop */
.sidebar-overlay {
  /* stylelint-disable-next-line at-rule-no-unknown */
  position: fixed;
  inset: 0px;
  z-index: 40;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
  --tw-bg-opacity: 0.75;
}

.sidebar-overlay.lg-hidden {
  /* stylelint-disable-next-line at-rule-no-unknown */
}

@media (min-width: 1024px) {
  .sidebar-overlay.lg-hidden {
    display: none;
  }
}

/* General overlay backdrop for modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 40;
}

.modal-overlay.dark {
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
  --tw-bg-opacity: 0.5;
}

.modal-overlay.light {
  background-color: transparent;
  --tw-bg-opacity: 0.5;
}

/* Backdrop blur effects */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* ========================================
   CONTAINER STYLES
   ======================================== */

/* Modal container base */
.modal-container {
  position: fixed;
  display: flex;
  cursor: move;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0px;
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-container.sample-style {
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  isolation: isolate;
  contain: layout style paint;
  resize: none;
  transition: box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  animation: modalAppear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalAppear {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-container.draggable {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.modal-container.no-backdrop {
  /* No backdrop styling - modal is draggable without overlay */
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

/* Modal container with motion/framer-motion support */
.modal-container-motion {
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding: 0px;
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.modal-container-motion.dragging {
  cursor: grabbing;
}

.modal-container-motion.default {
  cursor: default;
}

/* Modal container positioning */
.modal-positioned {
  position: fixed;
}

.modal-positioned.dragging {
  cursor: grabbing;
}

.modal-positioned.default {
  cursor: default;
}

/* Modal z-index utilities - removed to prevent overrides */

/* Draggable modal utility class */
.draggable-modal {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Drag handle utility class */
.drag-handle {
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Modal dynamic styles (from modal-system.tsx) */
.modal-container-dynamic {
  position: fixed;
}

.modal-container-dragging {
  cursor: grabbing;
}

.modal-container-default {
  cursor: default;
}

/* Modal component specific containers */
.modal-payment-container {
  /* Dynamic width and height will be set via inline styles */
  position: relative;
}

.modal-project-container {
  /* Dynamic width and height will be set via inline styles */
  position: relative;
}

.modal-blog-container {
  /* Dynamic top and left will be set via inline styles */
}

/* ========================================
   HEADER STYLES
   ======================================== */

/* Modal header base styles */
.modal-header {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-bottom: 2px solid #1d4ed8;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: -8px;
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  opacity: 1;
  background-color: transparent;
}

/* Modal header with motion support and icon colors - matching sample modal */
.modal-header-motion {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border-bottom: 2px solid #1d4ed8;
  padding: 16px 24px;
  height: 80px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: -8px;
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  opacity: 1;
  background-color: transparent;
}

.modal-header-motion.blue-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}

.modal-header-motion.green-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);
}

.modal-header-motion.purple-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}

.modal-header-motion.orange-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(194 65 12 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}

.modal-header-motion.red-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}

/* Modal header content and icon styles */
.modal-header-content {
  flex: 1;
}

.modal-header-icon {
  padding: 0px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-icon svg {
  width: 24px;
  height: 24px;
  color: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-header-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-header-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
}

.modal-header-close {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.modal-header-close svg {
  width: 20px;
  height: 20px;
  color: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-header-motion-content {
  display: flex;
  align-items: center;
}

.modal-header-motion-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-header-motion-icon {
  padding: 0px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.modal-header-motion-icon svg {
  width: 24px;
  height: 24px;
  stroke: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

.modal-header-motion-text {
  flex: 1;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.modal-header-motion-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
  line-height: 1.2;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-header-motion-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.3;
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
}

.modal-header-motion-close {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header-motion-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-header-motion-close svg {
  width: 20px;
  height: 20px;
  stroke: white;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

/* ========================================
   BODY STYLES
   ======================================== */

/* Modal content base */
.modal-content {
  flex: 1 1 0%;
  cursor: default;
}

/* Custom scrollbar styling for modal content */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Firefox scrollbar styling */
.modal-content {
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;
}

.modal-content-motion {
  flex: 1 1 0%;
  cursor: default;
  overflow-y: auto;
}

/* Modal form styles */
.modal-form > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.modal-form {
  padding: 1rem;
}

.modal-form.compact > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form.compact {
  padding: 0.75rem;
}

/* Modal form sections */
.modal-form-section > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form-section.compact > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.modal-form-section-motion {
  background-color: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.modal-form-section-motion.compact {
  padding: 0.75rem;
}

.modal-form-section-motion.default {
  padding: 1rem;
}

/* Removed - icons are now directly inside titles */

.modal-form-section-motion-header.compact {
  margin-bottom: 0.5rem;
}

.modal-form-section-motion-header.default {
  margin-bottom: 0.75rem;
}

.modal-form-section-motion-icon {
  display: flex;
  height: 1.25rem;
  width: 1.25rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
}

.modal-form-section-motion-icon.blue {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.green {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.purple {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.orange {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-icon.red {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.modal-form-section-motion-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Modal form fields */
.modal-form-field {
  /* Container for form field */
  display: flex;
  flex-direction: column;
}

.modal-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.modal-form-grid.no-bottom-space {
  margin-bottom: 0;
}

.modal-form-grid.no-bottom-space > div:last-child {
  margin-bottom: 0;
}

.modal-form-grid.compact {
  gap: 0.75rem;
}

.modal-form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.modal-required {
  color: #dc2626;
  font-weight: 600;
}

.modal-form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-input-error:focus {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-input.with-icon {
  padding-left: 2.5rem;
}

.modal-form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
  resize: vertical;
}

.modal-form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-form-textarea.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-select.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-checkbox.modal-form-input-error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.modal-form-checkbox {
  margin-top: 0.25rem;
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-form-checkbox:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-form-error {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.modal-form-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  height: 1.25rem;
  width: 1.25rem;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

/* Modal buttons */
.modal-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-weight: 500;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.modal-button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-offset-width: 2px;
}
.modal-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.modal-button.primary {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.modal-button.primary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.modal-button.secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.modal-button.secondary:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity, 1));
}

.modal-button.outline {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.modal-button.outline:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-button.outline:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.modal-button.danger {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.danger:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.modal-button.danger:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.modal-button.success {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.success:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.modal-button.success:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-button.warning {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.modal-button.warning:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}

.modal-button.warning:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));
}

.modal-button.sm {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-button.md {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-button.lg {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

.modal-button-loading-spinner {
  margin-right: 0.5rem;
  height: 1rem;
  width: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.modal-button-loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 9999px;
  border-bottom-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

/* Sample modal button styles - using buttons.css classes instead */

/* Modal cards & sections */
.modal-card {
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 1rem;
}

.modal-card.blue-gradient {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}

.modal-card.gray {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-card.white {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
}

.modal-card-header {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-card-title {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-card-amount {
  text-align: right;
}

.modal-card-amount-value {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.modal-card-amount-label {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-card-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.modal-card-content {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-card-row {
  display: flex;
  justify-content: space-between;
}

.modal-card-row-label {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.modal-card-row-value {
  font-weight: 500;
}

.modal-card-row.discount {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-card-row.fee {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

/* Modal dropdown styles */
.modal-dropdown {
  position: relative;
}

.modal-dropdown-button {
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
}

.modal-dropdown-button:focus {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-dropdown-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-dropdown-selection {
  display: flex;
  align-items: center;
}

.modal-dropdown-icon {
  margin-right: 0.75rem;
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.modal-dropdown-text {
  /* Container for dropdown text */
  display: flex;
  flex-direction: column;
}

.modal-dropdown-title {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-dropdown-description {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-dropdown-arrow {
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.modal-dropdown-arrow.open {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.modal-dropdown-menu {
  position: absolute;
  margin-top: 0.25rem;
  max-height: 15rem;
  width: 100%;
  overflow: auto;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.modal-dropdown-item {
  width: 100%;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
}

.modal-dropdown-item:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-dropdown-item:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.modal-dropdown-item.selected {
  border-left-width: 4px;
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.modal-dropdown-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-dropdown-item-icon {
  margin-right: 0.75rem;
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.modal-dropdown-item-text {
  /* Container for item text */
  display: flex;
  flex-direction: column;
}

.modal-dropdown-item-title {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-dropdown-item-description {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-dropdown-item-fee {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

/* Modal status messages */
.modal-message {
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 0.75rem;
}

.modal-message.success {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.modal-message.error {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.modal-message.warning {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.modal-message.info {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.modal-message-content {
  display: flex;
  align-items: center;
}

.modal-message-icon {
  margin-right: 0.5rem;
  height: 1.25rem;
  width: 1.25rem;
}

.modal-message-icon.success {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-message-icon.error {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.modal-message-icon.warning {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.modal-message-icon.info {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.modal-message-text {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
}

.modal-message-text.success {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.modal-message-text.error {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.modal-message-text.warning {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.modal-message-text.info {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

/* Modal checkbox sections */
.modal-checkbox-section > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-checkbox-item {
  display: flex;
  align-items: flex-start;
}

.modal-checkbox-label {
  margin-left: 0.75rem;
}

.modal-checkbox-title {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-checkbox-description {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-checkbox-input {
  margin-top: 0.5rem;
}

/* Modal promo code section */
.modal-promo-input {
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-transform: uppercase;
}
.modal-promo-input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}

.modal-promo-success {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.modal-promo-success-icon {
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-promo-message {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

/* Modal security & payment sections */
.modal-security-section {
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
  padding: 1rem;
}

.modal-security-header {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.modal-security-icon {
  margin-right: 0.5rem;
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.modal-security-title {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-security-status {
  margin-bottom: 0.75rem;
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 0.75rem;
}

.modal-security-status.creating {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.modal-security-status.ready {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.modal-security-status-content {
  display: flex;
  align-items: center;
}

.modal-security-status-spinner {
  margin-right: 0.5rem;
  height: 1rem;
  width: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.modal-security-status-spinner {
  animation: spin 1s linear infinite;
  border-radius: 9999px;
  border-bottom-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}

.modal-security-status-icon {
  margin-right: 0.5rem;
  height: 1rem;
  width: 1rem;
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-security-status-text {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.modal-security-status-text.creating {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.modal-security-status-text.ready {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.modal-security-card {
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding: 0.75rem;
}

.modal-security-footer {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.modal-security-footer-icon {
  margin-right: 0.25rem;
  height: 0.75rem;
  width: 0.75rem;
}

/* Modal resize handle - invisible border handles like sample modal */
.modal-resize-handle {
  position: absolute;
  background: transparent;
}

/* Border resize handles - 4px borders like sample modal */
.modal-resize-handle-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  cursor: n-resize;
}

.modal-resize-handle-right {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  cursor: e-resize;
}

.modal-resize-handle-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  cursor: s-resize;
}

.modal-resize-handle-left {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  cursor: w-resize;
}

/* Note: All resize handle variants removed - using border handles only like sample modal */

/* Ensure no corner resize handles appear - only border handles */
.modal-container {
  resize: none;
  overflow: hidden;
}

.modal-container * {
  resize: none;
}

/* Prevent any browser default resize handles */
.modal-container,
.modal-container *,
.modal-container::before,
.modal-container::after {
  resize: none;
  overflow: hidden;
}


/* Modal content specific styles */
.modal-project-content {
  height: calc(100% - 80px);
}

.modal-blog-content {
  cursor: default;
}

.modal-blog-form-content {
  /* Dynamic max-height and min-height will be set via inline styles */
  position: relative;
}

.modal-blog-view-mode-text {
  background-color: transparent;
}

/* ========================================
   MODAL FORM SECTIONS
   ======================================== */

.modal-form-section {
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0.75rem;
}

.modal-form-section.sample-style {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  margin-bottom: 0;
}

.modal-form-section-header.sample-style {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.modal-form-section-title.sample-style {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.modal-form-section-icon.sample-style {
  width: 16px;
  height: 16px;
  color: #3b82f6;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.modal-form-section-icon.sample-style.green {
  color: #10b981;
}

.modal-form-section-icon.sample-style.orange {
  color: #f59e0b;
}

.modal-form-section-header {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.modal-form-section-header > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-form-section-icon {
  height: 1rem;
  width: 1rem;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.modal-form-section-title {
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.modal-form-section-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form-section-content.sample-style > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.modal-form-grid.sample-style {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0px;
}

.modal-form-input.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-input.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

.modal-form-select.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.modal-form-select.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

.modal-form-textarea.sample-style {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.modal-form-textarea.sample-style:focus {
  border-color: #3b82f6;
  outline: none;
}

/* ========================================
   MODAL CONTENT STRUCTURE
   ======================================== */

.modal-content {
  display: flex;
  flex-direction: column;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  padding: 0px;
}

.modal-content-body {
  flex: 1 1 0%;
  cursor: default;
}

.modal-content.sample-style {
  display: flex;
  flex-direction: column;
  opacity: 1;
  background-color: transparent;
  flex: 1;
}

.modal-content-body.sample-style {
  flex: 1;
  padding: 20px;
  cursor: default;
  overflow-y: visible;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modal-content-wrapper.sample-style {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modal-form > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

/* ========================================
   MODAL FOOTER
   ======================================== */

.modal-footer {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
  padding: 0.75rem;
}

.modal-footer-buttons {
  display: flex;
  justify-content: center;
}

.modal-footer-buttons > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-footer.sample-style {
  background-color: white;
  padding: 24px 24px 0 24px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 60px;
  opacity: 1;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.modal-footer.no-padding {
  padding: 6px 8px 0 8px;
}

.modal-footer-buttons.sample-style {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
  padding: 8px 16px;
  min-height: 48px;
}

/* Modal status message styles (matching sample modal) */
.modal-status-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
}

.modal-status-message svg {
  width: 16px;
  height: 16px;
  color: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* ========================================
   MODAL RESIZE HANDLE
   ======================================== */

/* Note: Modal resize handles are defined above as invisible border handles */

/* ========================================
   MODAL HEADER COMPONENTS
   ======================================== */

.modal-header-icon {
  border-radius: 0.375rem;
  background-color: rgb(0 0 0 / 0.1);
  padding: 0.25rem;
}

.modal-header-icon-svg {
  height: 2rem;
  width: 2rem;
  color: transparent;
}

.modal-header-content {
  flex: 1 1 0%;
}

.modal-header-title {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.modal-header-subtitle {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  color: rgb(255 255 255 / 0.8);
  background-color: transparent;
  background: none;
  background-image: none;
  background-clip: unset;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.modal-close-button {
  border-radius: 0.375rem;
  padding: 0.25rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.modal-close-button:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.modal-close-icon {
  height: 1.5rem;
  width: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

/* ========================================
   ADMIN/CLIENT DASHBOARD MODAL OVERRIDES
   ======================================== */

.admin-page .modal-container,
.admin-page .modal-container-motion,
.client-page .modal-container,
.client-page .modal-container-motion {
  background-color: #ffffff;
  color: #111827;
  border-color: #e5e7eb;
}

/* Modal form inputs override for admin/client dashboards */
.admin-page .modal-form-input,
.admin-page .modal-form-select,
.admin-page .modal-form-textarea,
.client-page .modal-form-input,
.client-page .modal-form-select,
.client-page .modal-form-textarea {
  background-color: #ffffff;
  color: #111827;
  border-color: #d1d5db;
}

.admin-page .modal-form-input::-moz-placeholder, .admin-page .modal-form-textarea::-moz-placeholder, .client-page .modal-form-input::-moz-placeholder, .client-page .modal-form-textarea::-moz-placeholder {
  color: #9ca3af;
}

.admin-page .modal-form-input::placeholder,
.admin-page .modal-form-textarea::placeholder,
.client-page .modal-form-input::placeholder,
.client-page .modal-form-textarea::placeholder {
  color: #9ca3af;
}

.modal-theme-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.modal-theme-green {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

/* Color utility classes for dynamic styling */
.modal-color-blue {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.modal-color-green {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.modal-color-purple {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}

.modal-color-orange {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}

.modal-color-red {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

/* Required field indicator */
.modal-required {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

/* Resize handle SVG */
.modal-resize-svg {
  height: 1rem;
  width: 1rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

/* Modal accessibility */
.modal-focus-trap {
  /* Focus trap styles if needed */
  position: relative;
  outline: none;
}

.modal-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.modal-aria-hidden {
  aria-hidden: true;
}

/* ========================================
   FOOTER STYLES
   ======================================== */

/* Modal footer base */
.modal-footer {
  margin-top: auto;
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding: 1rem;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.modal-footer.centered {
  display: flex;
  justify-content: center;
}

.modal-footer.centered > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-footer.left-aligned {
  display: flex;
  justify-content: flex-end;
}

.modal-footer.left-aligned > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.modal-footer.compact {
  padding: 0.75rem;
}

.modal-footer-basic {
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
  padding: 1rem;
}

/* ========================================
   TRANSITIONS & ANIMATIONS
   ======================================== */

/* Modal animation utilities */
.modal-fade-in {
  transition-property: opacity;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-slide-in {
  transition-property: transform;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-scale-in {
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========================================
   INVOICE MODAL STYLES
   ======================================== */

.invoice-details-container {
  background-color: #e9ecef;
  border: 1px solid #e9ecef;
}

.invoice-details-title {
  color: #495057;
}

.invoice-details-icon {
  color: #6c757d;
}

.invoice-header-desc {
  background-color: #d1d5db;
  color: #374151;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.invoice-header-qty,
.invoice-header-price,
.invoice-header-total {
  background-color: #d1d5db;
  color: #374151;
}

.invoice-header-del {
  background-color: #d1d5db;
  color: #374151;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.invoice-item-row {
  margin: 0;
  padding: 2px 0;
}

.invoice-summary-container {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.invoice-summary-title {
  color: #495057;
}

.invoice-summary-icon {
  color: #6c757d;
}

.invoice-summary-subtotal {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
}

.invoice-summary-subtotal-label {
  color: #1565c0;
}

.invoice-summary-subtotal-value {
  color: #0d47a1;
}

.invoice-summary-tax {
  background-color: #fff3e0;
  border: 1px solid #ffcc02;
}

.invoice-summary-tax-label {
  color: #e65100;
}

.invoice-summary-tax-value {
  color: #bf360c;
}

.invoice-summary-total {
  background-color: #e8f5e8;
  border: 1px solid #c8e6c9;
}

.invoice-summary-total-label {
  color: #2e7d32;
}

.invoice-summary-total-value {
  color: #1b5e20;
}

/* ========================================
   RESPONSIVE UTILITIES
   ======================================== */

@media (max-width: 768px) {
  .modal-form-grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .modal-header-content > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  
  .modal-header-icon svg {
    height: 1.5rem;
    width: 1.5rem;
  }
  
  .modal-header-title {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  .modal-header-subtitle {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/index.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* ========================================
   COMPONENTS INDEX
   Import all component styles in one place
   ======================================== */
/* sidebar.css is imported directly in admin-dashboard/layout.tsx */

/* ========================================
   RESPONSIVE TABLE SCROLLBAR STYLING
   ======================================== */

/* Dark gray scrollbar for responsive tables */
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* Firefox scrollbar styling */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: #6b7280 #f1f5f9;
}

/* ========================================
   MAIN PAGE VERTICAL SCROLLBAR STYLING
   ======================================== */

/* Dark gray scrollbar for main page vertical scroll */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* Firefox main scrollbar styling */
html {
  scrollbar-width: thin;
  scrollbar-color: #6b7280 #f1f5f9;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/components/sidebar.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* Sidebar-specific styles - completely isolated from admin page content */

/* Desktop Sidebar */
.sidebar-desktop {
  /* Main navigation items */
  .sidebar-main-nav {
    font-size: 1.125rem; /* 18px - bigger than text-base */
    font-weight: 600;
  }
  
  .admin-page .sidebar-main-nav .sidebar-icon {
    width: 1.5rem; /* 24px */
    height: 1.5rem; /* 24px */
  }
  
  /* Sub-navigation items */
  .sidebar-sub-nav {
    font-size: 1rem; /* 16px - bigger than text-sm */
    font-weight: 500;
  }
  
  .admin-page .sidebar-sub-nav .sidebar-icon {
    width: 1.25rem; /* 20px */
    height: 1.25rem; /* 20px */
  }
  
  /* Section headers */
  .sidebar-section-header {
    font-size: 0.75rem; /* 12px */
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  /* Logo text */
  .sidebar-logo-text {
    font-size: 1.125rem; /* 18px */
    font-weight: 700;
  }
  
  .sidebar-logo-subtext {
    font-size: 0.75rem; /* 12px */
    font-weight: 500;
  }
  
  /* Colorful Icons - Main Navigation */
  .admin-page .sidebar-main-nav .sidebar-icon {
    color: #3b82f6; /* Blue for main nav */
  }
  
  /* Colorful Icons - Sub Navigation */
  .admin-page .sidebar-sub-nav .sidebar-icon {
    color: #6b7280; /* Gray for sub nav */
  }
  
  /* Specific icon colors for different sections */
  /* Content Management Icons */
  .admin-page .sidebar-sub-nav[href*="visual-editor"] .sidebar-icon {
    color: #8b5cf6; /* Purple */
  }
  .admin-page .sidebar-sub-nav[href*="services"] .sidebar-icon {
    color: #10b981; /* Emerald */
  }
  .admin-page .sidebar-sub-nav[href*="team-members"] .sidebar-icon {
    color: #f59e0b; /* Amber */
  }
  .admin-page .sidebar-sub-nav[href*="technologies"] .sidebar-icon {
    color: #ef4444; /* Red */
  }
  .admin-page .sidebar-sub-nav[href*="testimonials"] .sidebar-icon {
    color: #06b6d4; /* Cyan */
  }
  .admin-page .sidebar-sub-nav[href*="blog"] .sidebar-icon {
    color: #84cc16; /* Lime */
  }
  .admin-page .sidebar-sub-nav[href*="legal-pages"] .sidebar-icon {
    color: #f97316; /* Orange */
  }
  
  /* Business Icons */
  .admin-page .sidebar-sub-nav[href*="jobs"] .sidebar-icon {
    color: #6366f1; /* Indigo */
  }
  .admin-page .sidebar-sub-nav[href*="clients"] .sidebar-icon {
    color: #ec4899; /* Pink */
  }
  .admin-page .sidebar-sub-nav[href*="clients-manager"] .sidebar-icon {
    color: #14b8a6; /* Teal */
  }
  .admin-page .sidebar-sub-nav[href*="projects"] .sidebar-icon {
    color: #8b5cf6; /* Purple */
  }
  .admin-page .sidebar-sub-nav[href*="invoices"] .sidebar-icon {
    color: #10b981; /* Emerald */
  }
  .admin-page .sidebar-sub-nav[href*="contact-forms"] .sidebar-icon {
    color: #f59e0b; /* Amber */
  }
  
  /* System Icons */
  .admin-page .sidebar-sub-nav[href*="data-upload"] .sidebar-icon {
    color: #06b6d4; /* Cyan */
  }
  .admin-page .sidebar-sub-nav[href*="chatbot"] .sidebar-icon {
    color: #84cc16; /* Lime */
  }
  .admin-page .sidebar-sub-nav[href*="users"] .sidebar-icon {
    color: #f97316; /* Orange */
  }
  .admin-page .sidebar-sub-nav[href*="settings"] .sidebar-icon {
    color: #6b7280; /* Gray */
  }
}

/* Mobile Sidebar */
.sidebar-mobile {
  /* Main navigation items */
  .sidebar-main-nav {
    font-size: 1.125rem; /* 18px - bigger than text-base */
    font-weight: 600;
  }
  
  .admin-page .sidebar-main-nav .sidebar-icon {
    width: 1.5rem; /* 24px */
    height: 1.5rem; /* 24px */
  }
  
  /* Sub-navigation items */
  .sidebar-sub-nav {
    font-size: 1rem; /* 16px - bigger than text-sm */
    font-weight: 500;
  }
  
  .admin-page .sidebar-sub-nav .sidebar-icon {
    width: 1.25rem; /* 20px */
    height: 1.25rem; /* 20px */
  }
  
  /* Section headers */
  .sidebar-section-header {
    font-size: 0.75rem; /* 12px */
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  /* Logo text */
  .sidebar-logo-text {
    font-size: 1.125rem; /* 18px */
    font-weight: 700;
  }
  
  .sidebar-logo-subtext {
    font-size: 0.75rem; /* 12px */
    font-weight: 500;
  }
  
  /* Colorful Icons - Main Navigation */
  .admin-page .sidebar-main-nav .sidebar-icon {
    color: #3b82f6; /* Blue for main nav */
  }
  
  /* Colorful Icons - Sub Navigation */
  .admin-page .sidebar-sub-nav .sidebar-icon {
    color: #6b7280; /* Gray for sub nav */
  }
  
  /* Specific icon colors for different sections */
  /* Content Management Icons */
  .admin-page .sidebar-sub-nav[href*="visual-editor"] .sidebar-icon {
    color: #8b5cf6; /* Purple */
  }
  .admin-page .sidebar-sub-nav[href*="services"] .sidebar-icon {
    color: #10b981; /* Emerald */
  }
  .admin-page .sidebar-sub-nav[href*="team-members"] .sidebar-icon {
    color: #f59e0b; /* Amber */
  }
  .admin-page .sidebar-sub-nav[href*="technologies"] .sidebar-icon {
    color: #ef4444; /* Red */
  }
  .admin-page .sidebar-sub-nav[href*="testimonials"] .sidebar-icon {
    color: #06b6d4; /* Cyan */
  }
  .admin-page .sidebar-sub-nav[href*="blog"] .sidebar-icon {
    color: #84cc16; /* Lime */
  }
  .admin-page .sidebar-sub-nav[href*="legal-pages"] .sidebar-icon {
    color: #f97316; /* Orange */
  }
  
  /* Business Icons */
  .admin-page .sidebar-sub-nav[href*="jobs"] .sidebar-icon {
    color: #6366f1; /* Indigo */
  }
  .admin-page .sidebar-sub-nav[href*="clients"] .sidebar-icon {
    color: #ec4899; /* Pink */
  }
  .admin-page .sidebar-sub-nav[href*="clients-manager"] .sidebar-icon {
    color: #14b8a6; /* Teal */
  }
  .admin-page .sidebar-sub-nav[href*="projects"] .sidebar-icon {
    color: #8b5cf6; /* Purple */
  }
  .admin-page .sidebar-sub-nav[href*="invoices"] .sidebar-icon {
    color: #10b981; /* Emerald */
  }
  .admin-page .sidebar-sub-nav[href*="contact-forms"] .sidebar-icon {
    color: #f59e0b; /* Amber */
  }
  
  /* System Icons */
  .admin-page .sidebar-sub-nav[href*="data-upload"] .sidebar-icon {
    color: #06b6d4; /* Cyan */
  }
  .admin-page .sidebar-sub-nav[href*="chatbot"] .sidebar-icon {
    color: #84cc16; /* Lime */
  }
  .admin-page .sidebar-sub-nav[href*="users"] .sidebar-icon {
    color: #f97316; /* Orange */
  }
  .admin-page .sidebar-sub-nav[href*="settings"] .sidebar-icon {
    color: #6b7280; /* Gray */
  }
}

/* Ensure sidebar styles don't affect admin page content */

/* Force sidebar visibility - ULTIMATE SIDEBAR FIX */
.admin-page .hidden.lg\:fixed.lg\:inset-y-0.lg\:flex.lg\:w-64.lg\:flex-col {
  display: flex;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  /* width: 16rem; - Removed to allow dynamic width */
  flex-direction: column;
  background-color: white;
  border-right: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  /* Remove any duplicate borders or shadows */
  border-left: none;
  border-top: none;
  border-bottom: none;
}

/* Force sidebar visibility on desktop screens */
@media (min-width: 1024px) {
  .admin-page .hidden.lg\:flex {
    display: flex;
  }
  
  .admin-page .sidebar-desktop {
    display: flex;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    /* width: 16rem; - Removed to allow dynamic width */
    flex-direction: column;
    background-color: white;
    border-right: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    /* Remove duplicate borders and shadows */
    border-left: none;
    border-top: none;
    border-bottom: none;
  }
}

/* Admin Page Title Icons - Same colors as sidebar */
/* Override default blue color for title icons to match sidebar colors */

/* Services Management - Emerald */
.admin-page .h-14.w-14.text-blue-600 {
  color: #10b981; /* Emerald - same as sidebar */
}

/* Clients Management - Teal (UserIcon) */
.admin-page .h-14.w-14.text-teal-600 {
  color: #14b8a6; /* Teal - same as sidebar */
}

/* Visual Editor - Purple */
.admin-page .h-14.w-14.text-purple-600 {
  color: #8b5cf6; /* Purple - same as sidebar */
}

/* Team Members - Amber */
.admin-page .h-14.w-14.text-amber-600 {
  color: #f59e0b; /* Amber - same as sidebar */
}

/* Technologies - Red */
.admin-page .h-14.w-14.text-red-600 {
  color: #ef4444; /* Red - same as sidebar */
}

/* Testimonials - Cyan */
.admin-page .h-14.w-14.text-cyan-600 {
  color: #06b6d4; /* Cyan - same as sidebar */
}

/* Blog - Lime */
.admin-page .h-14.w-14.text-lime-600 {
  color: #84cc16; /* Lime - same as sidebar */
}

/* Legal Pages - Orange */
.admin-page .h-14.w-14.text-orange-600 {
  color: #f97316; /* Orange - same as sidebar */
}

/* Jobs - Indigo */
.admin-page .h-14.w-14.text-indigo-600 {
  color: #6366f1; /* Indigo - same as sidebar */
}

/* Clients - Pink */
.admin-page .h-14.w-14.text-pink-600 {
  color: #ec4899; /* Pink - same as sidebar */
}

/* Projects - Purple */
.admin-page .h-14.w-14.text-purple-600 {
  color: #8b5cf6; /* Purple - same as sidebar */
}

/* Invoices - Emerald */
.admin-page .h-14.w-14.text-emerald-600 {
  color: #10b981; /* Emerald - same as sidebar */
}

/* Contact Forms - Amber */
.admin-page .h-14.w-14.text-amber-600 {
  color: #f59e0b; /* Amber - same as sidebar */
}

/* Data Upload - Cyan */
.admin-page .h-14.w-14.text-cyan-600 {
  color: #06b6d4; /* Cyan - same as sidebar */
}

/* Chatbot - Lime */
.admin-page .h-14.w-14.text-lime-600 {
  color: #84cc16; /* Lime - same as sidebar */
}

/* Users - Orange */
.admin-page .h-14.w-14.text-orange-600 {
  color: #f97316; /* Orange - same as sidebar */
}

/* Settings - Gray */
.admin-page .h-14.w-14.text-gray-600 {
  color: #6b7280; /* Gray - same as sidebar */
}

/* Dashboard - Blue */
.admin-page .h-14.w-14.text-blue-600 {
  color: #3b82f6; /* Blue - same as sidebar */
}

/* Sidebar Collapse Support */
.admin-page .sidebar-collapsed {
  width: 4rem;
}

.admin-page .sidebar-expanded {
  width: 16rem;
}

/* Responsive Sidebar Behavior */
/* Hide desktop sidebar on mobile/tablet */
@media (max-width: 1023px) {
  .admin-page .hidden.lg\:flex {
    display: none !important;
  }
}

/* Show desktop sidebar only on desktop */
@media (min-width: 1024px) {
  .admin-page .hidden.lg\:flex {
    display: flex !important;
  }
}

/* Mobile sidebar overlay and positioning */
.admin-page .sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
}

/* Ensure mobile sidebar is hidden on desktop */
@media (min-width: 1024px) {
  .admin-page .sidebar-mobile {
    display: none !important;
  }
}

/* Ensure sidebar is above any background containers */
.admin-page .sidebar-collapsed,
.admin-page .sidebar-expanded {
  position: fixed;
  background-color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  /* Ensure no duplicate backgrounds or borders */
  background-image: none;
  background-clip: padding-box;
}

/* Prevent sidebar styles from leaking to admin content */
.admin-page main,
.admin-page .admin-content,
.admin-page .admin-table,
.admin-page .admin-form {
  /* Reset any potential font inheritance */
  font-size: inherit;
  font-weight: inherit;
}

/* FORCE COLORFUL ICONS - ULTIMATE OVERRIDE */
.admin-page .sidebar-main-nav .sidebar-icon,
.admin-page .sidebar-sub-nav .sidebar-icon {
  color: inherit;
}

/* Specific colorful overrides for each section */
.admin-page .sidebar-sub-nav[href*="visual-editor"] .sidebar-icon { color: #8b5cf6; }
.admin-page .sidebar-sub-nav[href*="services"] .sidebar-icon { color: #10b981; }
.admin-page .sidebar-sub-nav[href*="team-members"] .sidebar-icon { color: #f59e0b; }
.admin-page .sidebar-sub-nav[href*="technologies"] .sidebar-icon { color: #ef4444; }
.admin-page .sidebar-sub-nav[href*="testimonials"] .sidebar-icon { color: #06b6d4; }
.admin-page .sidebar-sub-nav[href*="blog"] .sidebar-icon { color: #84cc16; }
.admin-page .sidebar-sub-nav[href*="legal-pages"] .sidebar-icon { color: #f97316; }
.admin-page .sidebar-sub-nav[href*="jobs"] .sidebar-icon { color: #6366f1; }
.admin-page .sidebar-sub-nav[href*="clients"] .sidebar-icon { color: #ec4899; }
.admin-page .sidebar-sub-nav[href*="clients-manager"] .sidebar-icon { color: #14b8a6; }
.admin-page .sidebar-sub-nav[href*="projects"] .sidebar-icon { color: #8b5cf6; }
.admin-page .sidebar-sub-nav[href*="invoices"] .sidebar-icon { color: #10b981; }
.admin-page .sidebar-sub-nav[href*="contact-forms"] .sidebar-icon { color: #f59e0b; }
.admin-page .sidebar-sub-nav[href*="data-upload"] .sidebar-icon { color: #06b6d4; }
.admin-page .sidebar-sub-nav[href*="chatbot"] .sidebar-icon { color: #84cc16; }
.admin-page .sidebar-sub-nav[href*="users"] .sidebar-icon { color: #f97316; }
.admin-page .sidebar-sub-nav[href*="settings"] .sidebar-icon { color: #6b7280; }

